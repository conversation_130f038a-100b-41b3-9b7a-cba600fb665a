import jwt from "jsonwebtoken";
import { connect } from "@config/db.js";
import DocumentManagement from "@models/DocumentManagement/DocumentManagement.Model.js";
import { NextResponse } from "next/server";

export const PUT = async (request, context) => {
  try {
    await connect(); // Connect to the database

    // Add JWT authentication
    const authHeader = request.headers.get("Authorization");
    if (!authHeader) {
      return NextResponse.json({ error: "Unauthorized - No Authorization header", status: 401 });
    }

    const token = authHeader && authHeader.split(" ")[1];
    if (!token) {
      return NextResponse.json({ error: "Invalid Authorization header format", status: 401 });
    }

    const secret = process.env.JWT_SECRET;
    let decoded;
    try {
      decoded = jwt.verify(token, secret);
    } catch (error) {
      console.error("JWT verification error:", error.message);
      return NextResponse.json({ error: "Invalid token", status: 401 });
    }

    const id = context.params.DocumentManagementID; // Extract DocumentManagementID from params
    const data = await request.json(); // Get the form data

    // Destructure the necessary fields including entityType
    const { documentname, entityType, description, isActive } = data;

    // Find the document by ID
    const documentManagement = await DocumentManagement.findById({ _id: id });

    if (!documentManagement) {
      return NextResponse.json({
        error: "DocumentManagement not found",
        status: 404,
      });
    }

    // Verify the document belongs to the same company
    const companyId = decoded?.userId;
    if (documentManagement.adminCompanyId !== companyId) {
      return NextResponse.json({
        error: "Unauthorized to update this document",
        status: 403,
      });
    }

    const update = {};
    if (documentname) {
      update.documentname = documentname.trim();
    }
    if (entityType && Array.isArray(entityType)) {
      update.entityType = entityType;
    }
    if (description) {
      update.description = description.trim();
    }
    if (isActive !== undefined) {
      update.isActive = isActive;
    }

    const updatedDoc = await DocumentManagement.findByIdAndUpdate(
      id,
      { $set: update },
      { new: true }
    );

    if (!updatedDoc) {
      return NextResponse.json(
        { error: 'DocumentManagement not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        message: 'DocumentManagement details updated successfully',
        data: updatedDoc,
        success: true,
      },
      { status: 200 }
    );
  } catch (err) {
    console.error('Error updating DocumentManagement:', err);
    return NextResponse.json(
      { error: 'Failed to update DocumentManagement' },
      { status: 500 }
    );
  }
};

// GET handler for retrieving a specific manufacturer by ID
export const GET = async (request, context) => {
  try {
    // Connect to the database
    await connect();

    // Extract the Manufacturer ID from the request parameters
    const id = context.params.DocumentManagementID; // Use context.params for accessing the parameters

    // Find the manufacturer by ID
    const Find_DocumentManagement = await DocumentManagement.findById({ _id: id });

    // Check if the manufacturer exists
    if (!Find_DocumentManagement) {
      return NextResponse.json({
        result: "No DocumentManagement Found",
        status: 404,
      });
    }

    // Return the found manufacturer as a JSON response
    return NextResponse.json({ result: Find_DocumentManagement, status: 200 });
  } catch (error) {
    console.error("Error fetching DocumentManagement:", error); // Log the error for debugging
    return NextResponse.json({
      result: "Failed to fetch DocumentManagement",
      status: 500,
    });
  }
};
// DELETE handler for deleting a document
export const DELETE = async (request, { params }) => {
  try {
    // Connect to the database
    await connect();

    // Add JWT authentication
    const authHeader = request.headers.get("Authorization");
    if (!authHeader) {
      return NextResponse.json({ error: "Unauthorized - No Authorization header", status: 401 });
    }

    const token = authHeader && authHeader.split(" ")[1];
    if (!token) {
      return NextResponse.json({ error: "Invalid Authorization header format", status: 401 });
    }

    const secret = process.env.JWT_SECRET;
    let decoded;
    try {
      decoded = jwt.verify(token, secret);
    } catch (error) {
      console.error("JWT verification error:", error.message);
      return NextResponse.json({ error: "Invalid token", status: 401 });
    }

    const { DocumentManagementID } = params; // Access the DocumentManagementID from params

    // Find the document first to check ownership
    const document = await DocumentManagement.findById(DocumentManagementID);

    if (!document) {
      return NextResponse.json({
        error: "DocumentManagement not found",
        status: 404,
      });
    }

    // Verify the document belongs to the same company
    const companyId = decoded?.userId;
    if (document.adminCompanyId !== companyId) {
      return NextResponse.json({
        error: "Unauthorized to delete this document",
        status: 403,
      });
    }

    // Delete the document
    await DocumentManagement.findByIdAndDelete(DocumentManagementID);

    return NextResponse.json({
      message: "DocumentManagement deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("Error deleting DocumentManagement:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the DocumentManagement",
      status: 500,
    });
  }
};
