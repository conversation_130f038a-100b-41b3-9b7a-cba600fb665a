import jwt from "jsonwebtoken";
import { connect } from "@config/db.js";
import VehicleDocument from "@models/VehicleDocuments/VehicleDocuments.Model.js";
import { catchAsyncErrors } from "@middlewares/catchAsyncErrors.js";
import { NextResponse } from "next/server";
// import Vehicle from "@models/Vehicle/Vehicle.Model.js";
// import Company from "@models/Company/Company.Model.js";

/* ---------------- CREATE ---------------- */
export const POST = catchAsyncErrors(async (request) => {
  await connect();

  /* ---------------- JWT AUTH ---------------- */
  const authHeader = request.headers.get("Authorization");
    if (!authHeader) {
     return NextResponse.json({ error: "Unauthorized", status: 401 });
   }

  const token  = authHeader || authHeader.split(" ")[1];
  const secret = process.env.JWT_SECRET;
  let decoded;
  try {
    decoded = jwt.verify(token, secret);
  } catch {
    return NextResponse.json({ error: "Invalid token", status: 401 });
  }

  const companyId = decoded?.userId;
  const companyName = decoded?.company;

  if (!companyId || !companyName) {
    return NextResponse.json({ error: "Company info missing in token", status: 400 });
  }

  const {
    document,
    documentname,
    Vehicleid,
    isActive,
    documentExpire,
    adminCreatedBy,
  } = await request.json();

const duplicate = await VehicleDocument.findOne({
  documentname,
  adminCompanyId: companyId,
  adminCompanyName: companyName,
  Vehicleid: Vehicleid,
});


  if (duplicate) {
    return NextResponse.json({
      error: "Document with the same name already exists for this company",
      status: 400,
    });
  }

  const newDoc = new VehicleDocument({
    document,
    documentname,
    Vehicleid,
    isActive,
    documentExpire,
    adminCreatedBy,
    adminCompanyId: companyId,
    adminCompanyName: companyName,
  });

  const saved = await newDoc.save();

  return NextResponse.json(
    saved
      ? { message: "Document created successfully", success: true, status: 200 }
      : { error: "Document not added", status: 400 }
  );
});



export const GET = async () => {
  try {
    await connect();
    const documents = await VehicleDocument.find()
      .populate('Vehicleid')
      .populate('adminCompanyId')
      .sort({ createdAt: -1 });
    return NextResponse.json({ success: true, documents });
  } catch (error) {
    return NextResponse.json({ error: error.message, status: 500 });
  }
};