"use client";
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Header from "@/app/Dashboard/Components/Header";
import Sidebar from "@/app/Dashboard/Components/Sidebar";
import { API_URL_DriverDocument } from "@/app/Dashboard/Components/ApiUrl/ApiUrls";
import { isAuthenticated } from "@/utils/verifytoken";
import { Icons, toast } from "react-toastify";
import Image from "next/image";
import { getUserName } from "@/utils/storageUtils";

const DocumentReportPage = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isVerifyModalOpen, setIsVerifyModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [verificationStatus, setVerificationStatus] = useState("");

  // Helper function to get full document URL
  const getDocumentUrl = (documentPath) => {
    if (!documentPath) return null;
    // If it's already a full URL, return as is
    if (documentPath.startsWith('http')) return documentPath;
    // If it starts with /uploads, prepend the base URL
    if (documentPath.startsWith('/uploads')) {
      return `${window.location.origin}${documentPath}`;
    }
    // Otherwise, assume it's a relative path
    return `${window.location.origin}/uploads/${documentPath}`;
  };

  // Helper function to get vehicle/driver display name
  const getEntityDisplayName = (document) => {
    if (!document) return 'Information Not Available';

    // If it's a driver document and we have driver info
    if (document.documentname === 'driver' && document.Driverid) {
      const firstName = document.Driverid.firstName || '';
      const lastName = document.Driverid.lastName || '';
      if (firstName || lastName) {
        return `Driver: ${firstName} ${lastName}`.trim();
      }
    }

    // If it's a vehicle document, try to get vehicle info
    if (document.documentname === 'vehicle' || document.documentname !== 'driver') {
      // For now, we'll use a generic vehicle name since vehicle info isn't in the current API response
      // This can be updated when vehicle information is available in the API
      return 'Vehicle: Information Not Available';
    }

    return 'Entity Information Not Available';
  };

  // Helper function to format date safely
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return 'Invalid Date';
    }
  };

  // Helper function to get status display info
  const getStatusDisplay = (status) => {
    const normalizedStatus = status || 'pending';
    switch (normalizedStatus) {
      case 'verified':
        return { color: 'bg-green-500', text: 'Verified', textColor: 'text-green-700' };
      case 'rejected':
        return { color: 'bg-red-500', text: 'Rejected', textColor: 'text-red-700' };
      default:
        return { color: 'bg-yellow-500', text: 'Pending', textColor: 'text-yellow-700' };
    }
  };

  // Fetch documents data
  const fetchDocuments = async () => {
    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      const response = await axios.get(API_URL_DriverDocument);
      if (response.data && response.data.result) {
        setDocuments(response.data.result);
      } else {
        setDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast.error("Failed to fetch document reports");
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  // Filter documents based on search term
  const filteredDocuments = documents.filter((doc) => {
    const searchLower = searchTerm.toLowerCase();
    const firstName = doc.Driverid?.firstName || '';
    const lastName = doc.Driverid?.lastName || '';
    const documentName = doc.documentname || '';
    const licenseNumber = doc.Driverid?.licenseNumber || '';
    const adminCompanyName = doc.adminCompanyName || '';
    const email = doc.Driverid?.email || '';

    return (
      firstName.toLowerCase().includes(searchLower) ||
      lastName.toLowerCase().includes(searchLower) ||
      documentName.toLowerCase().includes(searchLower) ||
      licenseNumber.toLowerCase().includes(searchLower) ||
      adminCompanyName.toLowerCase().includes(searchLower) ||
      email.toLowerCase().includes(searchLower)
    );
  });



  // Action handlers
  const handleView = (documentId) => {
    // Implement view functionality
    console.log("View document:", documentId);
    toast.info("View functionality to be implemented");
  };

  const handleVerify = (documentId) => {
    // Find the document and open modal
    const document = documents.find(doc => doc._id === documentId);
    if (document) {
      setSelectedDocument(document);
      // Set current verification status or default to empty for new selection
      setVerificationStatus(document.verificationStatus || "");
      setIsVerifyModalOpen(true);
    }
  };

  const handleVerifySubmit = async () => {
    if (!selectedDocument || !verificationStatus) {
      toast.error("Please select a verification status");
      return;
    }

    try {
      if (!isAuthenticated()) {
        toast.error("Please login to access this page");
        return;
      }

      const token = localStorage.getItem("token");
      const verifiedBy = getUserName() || "Unknown User";

      const updateData = {
        verificationStatus,
        verifiedBy,
      };

      const response = await axios.put(
        `${API_URL_DriverDocument}/${selectedDocument._id}`,
        updateData,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data && response.data.message) {
        toast.success(`Document ${verificationStatus} successfully!`);
        setIsVerifyModalOpen(false);
        setSelectedDocument(null);
        setVerificationStatus("");

        // Refresh the documents list to show updated status
        fetchDocuments();
      } else {
        toast.error("Failed to update document verification status");
      }
    } catch (error) {
      console.error("Error verifying document:", error);
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to verify document. Please try again.");
      }
    }
  };

  const closeVerifyModal = () => {
    setIsVerifyModalOpen(false);
    setSelectedDocument(null);
    setVerificationStatus("");
  };

  const handleVerifyDocument = () => {
    // Implement verify document functionality
    console.log("Verify Document clicked");
    toast.info("Verify Document functionality to be implemented");
  };

  const handleAddDocumentType = () => {
    toast.info("Add Document Type functionality to be implemented");
  };

  return (
    <div className="h-[100vh] overflow-hidden">
      <Header className="min-w-full" />
      <div className="flex gap-4">
        <Sidebar />
        <div
          className="w-[80%] xl:w-[85%] h-screen flex flex-col justify-start overflow-y-auto pr-4"
          style={{
            height: "calc(100vh - 90px)",
          }}
        >
          <h1 className="text-[#313342] font-medium text-2xl py-5 pb-8 flex gap-2 items-center">
            <div className="myborder flex gap-3 border-2 border-t-0 border-l-0 border-r-0">
              <span className="opacity-65">Reports</span>
              <div className="flex items-center gap-3 myborder2">
                <span>
                  <svg
                    width="8"
                    height="16"
                    viewBox="0 0 8 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="w-2 h-4 object-cover object-center"
                  >
                    <path
                      d="M0.5 1L7.5 8L0.5 15"
                      stroke="#313342"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
                <span>Document Reports</span>
              </div>
            </div>
          </h1>

          {/* Search Bar and Action Buttons */}
          <div className="flex justify-between items-center mb-6">
            {/* Search Bar - Left Side */}
            <div className="relative">
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="text"
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-4 py-2 w-64 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Action Buttons - Right Side */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleVerifyDocument}
                className="px-4 py-2 text-white bg-[#38384A] rounded-md hover:text-white bg-[#38384A] transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 font-medium"
              >
                Verify Document
              </button>
              <button
                onClick={handleAddDocumentType}
                className="px-4 py-2 text-white bg-[#38384A] rounded-md text-white bg-[#38384A] transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium"
              >
                Add Document Type
              </button>
            </div>
          </div>

          {/* Table */}
          <div className="py-5">
            <div className="drop-shadow-custom4">
              <div className="overflow-x-auto custom-scrollbar">
                <table className="w-full bg-white border table-auto">
                  <thead className="font-sans font-bold text-sm text-center">
                    <tr className="text-white bg-[#38384A]">
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Entity
                      </th>
                      <th className="py-3 px-4 min-w-[200px] w-[200px] text-center text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Name
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        MOT
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Registration
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Insurance
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        License
                      </th>
                      <th className="py-3 px-4 min-w-[150px] w-[150px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Maintenance Log
                      </th>
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Status
                      </th>
                      <th className="py-3 px-4 min-w-[120px] w-[120px] text-white bg-custom-bg whitespace-normal break-all overflow-hidden">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="font-sans font-medium text-sm">
                    {loading ? (
                      <tr>
                        <td colSpan="9" className="py-8 text-center text-gray-500">
                          <div className="flex justify-center items-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <span className="ml-2">Loading...</span>
                          </div>
                        </td>
                      </tr>
                    ) : filteredDocuments.length === 0 ? (
                      <tr>
                        <td colSpan="9" className="py-8 text-center text-gray-500">
                          {searchTerm ? `No documents found matching "${searchTerm}"` : "No documents found"}
                        </td>
                      </tr>
                    ) : (
                      filteredDocuments.map((doc, index) => (
                        <tr key={doc._id || index} className="border-b text-center">
                          <td className="py-3 px-4 min-w-[120px] w-[120px] text-center whitespace-normal break-all overflow-hidden">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              doc.documentname === 'driver'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {doc.documentname === 'driver' ? 'Driver' : 'Vehicle'}
                            </span>
                          </td>
                          <td className="py-3 px-4 min-w-[200px] w-[200px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid && (doc.Driverid.firstName || doc.Driverid.lastName)
                              ? `${doc.Driverid.firstName || ''} ${doc.Driverid.lastName || ''}`.trim()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.taxiBadgeDate
                              ? new Date(doc.Driverid.taxiBadgeDate).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.licenseNumber || 'N/A'}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.insurance || 'N/A'}
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.Driverid?.licenseExpiryDate
                              ? new Date(doc.Driverid.licenseExpiryDate).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[150px] w-[150px] text-center whitespace-normal break-all overflow-hidden">
                            {doc.documentExpire
                              ? new Date(doc.documentExpire).toLocaleDateString()
                              : 'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 min-w-[120px] w-[120px] text-center whitespace-normal break-all overflow-hidden">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              (doc.verificationStatus || 'pending') === 'verified'
                                ? 'bg-green-100 text-green-800'
                                : (doc.verificationStatus || 'pending') === 'rejected'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {(doc.verificationStatus || 'pending') === 'verified'
                                ? 'Verified'
                                : (doc.verificationStatus || 'pending') === 'rejected'
                                ? 'Rejected'
                                : 'Pending'
                              }
                            </span>
                          </td>
                          <td className="py-3 px-4 min-w-[120px] w-[120px] whitespace-normal break-all overflow-hidden text-center">
                            <div className="flex gap-4 justify-center">
                              <button
                                onClick={() => handleView(doc._id)}
                                className="transition-colors p-1 hover:bg-blue-50 rounded"
                                title="View Details"
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-blue-600"
                                >
                                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                                  <circle cx="12" cy="12" r="3" />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleVerify(doc._id)}
                                className="transition-colors p-1 hover:bg-green-50 rounded"
                                title="Verify Document"
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="text-green-600"
                                >
                                  <path d="M9 12l2 2 4-4" />
                                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
                                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
                                  <path d="M12 21c0-1-1-3-3-3s-3 2-3 3 1 3 3 3 3-2 3-3" />
                                  <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Verify Document Modal */}
      {isVerifyModalOpen && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-5xl max-h-[95vh] overflow-y-auto shadow-2xl">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Verify Document</h2>
              <button
                onClick={closeVerifyModal}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Vehicle/Driver Info */}
            <div className="px-6 py-4 bg-gray-50">
              <p className="text-base text-gray-700">
                <span className="text-gray-900">
                  {getEntityDisplayName(selectedDocument)}
                </span>
              </p>
            </div>

            {/* Document Table */}
            <div className="px-6 py-4">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-0 font-medium text-gray-600 text-sm">Document Type</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Uploaded On</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Expiry Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Registration Document */}
                  <tr className="border-b border-gray-100">
                    <td className="py-3 px-0 text-gray-700 text-sm">Registration</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2025-06-15</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2026-06-10</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-700">Verified</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </button>
                    </td>
                  </tr>

                  {/* Insurance Document - Selected/Highlighted */}
                  <tr className="border-b border-gray-100 bg-blue-50">
                    <td className="py-3 px-0 text-gray-700 text-sm font-medium">Insurance</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2024-04-28</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2024-05-01</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-700">New</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-orange-600 hover:text-orange-800 text-sm flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                          </svg>
                          Verify
                        </button>
                        <span className="text-gray-300">/</span>
                        <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Maintenance Log */}
                  <tr className="border-b border-gray-100">
                    <td className="py-3 px-0 text-gray-700 text-sm">Maintenance Log</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2025-01-01</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2025-12-30</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-700">Verified</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </button>
                    </td>
                  </tr>

                  {/* Pollution Cert */}
                  <tr className="border-b border-gray-100">
                    <td className="py-3 px-0 text-gray-700 text-sm">Pollution Cert.</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">—</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">—</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-700">About to Expire</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-gray-600 hover:text-gray-800 text-sm">Notify</button>
                        <span className="text-gray-300">/</span>
                        <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Insurance (Expired) */}
                  <tr className="border-b border-gray-100">
                    <td className="py-3 px-0 text-gray-700 text-sm">Insurance</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2024-04-28</td>
                    <td className="py-3 px-4 text-gray-700 text-sm">2024-05-01</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-700">Expired</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-gray-600 hover:text-gray-800 text-sm">Notify</button>
                        <span className="text-gray-300">/</span>
                        <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Document Preview and Verification Section */}
            <div className="px-6 py-4 flex gap-8">
              {/* Document Preview */}
              <div className="flex-shrink-0">
                {selectedDocument.document ? (
                  <div className="w-40 h-48 border border-gray-300 rounded-lg overflow-hidden bg-gray-50">
                    {selectedDocument.document.toLowerCase().includes('.pdf') ? (
                      // PDF Preview
                      <div className="w-full h-full flex flex-col items-center justify-center">
                        <div className="w-24 h-32 border border-gray-200 rounded bg-white flex flex-col items-center justify-center mb-2">
                          <div className="text-lg font-bold text-red-600 mb-1">PDF</div>
                          <div className="w-16 h-1 bg-gray-300 rounded mb-1"></div>
                          <div className="w-12 h-1 bg-gray-300 rounded mb-1"></div>
                          <div className="w-14 h-1 bg-gray-300 rounded"></div>
                        </div>
                        <button
                          onClick={() => window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                          className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                        >
                          View Full Document
                        </button>
                      </div>
                    ) : (
                      // Image Preview
                      <div className="relative w-full h-full">
                        <img
                          src={getDocumentUrl(selectedDocument.document)}
                          alt="Document preview"
                          className="w-full h-full object-cover cursor-pointer"
                          onClick={() => window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                        <div className="w-full h-full flex flex-col items-center justify-center" style={{display: 'none'}}>
                          <div className="text-sm text-gray-500 mb-2">Document Preview</div>
                          <div className="text-xs text-gray-400">Click to view full document</div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  // No document available
                  <div className="w-40 h-48 border border-gray-300 rounded-lg bg-gray-50 flex flex-col items-center justify-center">
                    <div className="w-24 h-32 border border-gray-200 rounded bg-white flex flex-col items-center justify-center mb-2">
                      <div className="text-lg font-bold text-gray-400 mb-1">N/A</div>
                      <div className="w-16 h-1 bg-gray-200 rounded mb-1"></div>
                      <div className="w-12 h-1 bg-gray-200 rounded mb-1"></div>
                      <div className="w-14 h-1 bg-gray-200 rounded"></div>
                    </div>
                    <div className="text-xs text-gray-400">No Document Available</div>
                  </div>
                )}
              </div>

              {/* Verification Options */}
              <div className="flex-1 flex items-center">
                <div className="flex items-center space-x-8">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="verificationStatus"
                      value="verified"
                      checked={verificationStatus === "verified"}
                      onChange={(e) => setVerificationStatus(e.target.value)}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Verified</span>
                  </label>
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="verificationStatus"
                      value="rejected"
                      checked={verificationStatus === "rejected"}
                      onChange={(e) => setVerificationStatus(e.target.value)}
                      className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
                    />
                    <span className="ml-3 text-sm text-gray-700">Rejected</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={closeVerifyModal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleVerifySubmit}
                className="px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 transition-colors text-sm font-medium"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentReportPage;
