"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import { API_URL_Document } from "../../../Components/ApiUrl/ApiUrls";
import Image from "next/image";

const UpdateDocumentModel = ({ isOpen, onClose, documentData, fetchData }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    documentname: "",
    entityType: "",
    isActive: true,
  });

  useEffect(() => {
    if (documentData && isOpen) {
      setFormData({
        documentname: documentData.documentname || "",
        entityType: Array.isArray(documentData.entityType) && documentData.entityType.length > 0 ? documentData.entityType[0] : "",
        isActive: documentData.isActive !== undefined ? documentData.isActive : true,
      });
    }
  }, [documentData, isOpen]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleEntityTypeChange = (entityType) => {
    setFormData(prev => ({
      ...prev,
      entityType: entityType
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate that an entity type is selected
    if (!formData.entityType) {
      toast.error("Please select an entity type (Driver or Vehicle)");
      return;
    }

    // Convert single entity type to array for API compatibility
    const submitData = {
      ...formData,
      entityType: [formData.entityType]
    };

    setLoading(true);

    try {
      // Get token from localStorage
      const token = localStorage.getItem("token");

      if (!token) {
        toast.error("Authentication token not found. Please login again.");
        setLoading(false);
        return;
      }

      const response = await axios.put(
        `${API_URL_Document}/${documentData._id}`,
        submitData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success || response.data.message) {
        toast.success(response?.data?.message || "Document updated successfully");
        fetchData();
        onClose();
      } else {
        toast.warn(response?.data?.error || "Failed to update document");
      }
    } catch (err) {
      console.error("Error updating document:", err);
      console.error("Error response:", err.response);
      toast.error(err.response?.data?.error || err.message || "Failed to update document");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-[50px] w-[528px] rounded-xl shadow-lg h-[380px]">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Update Document
          </h2>

          <Image
            width={15}
            height={15}
            alt="cross"
            src="/crossIcon.svg"
            className="cursor-pointer"
            onClick={onClose}
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="text-[10px] mb-2 block">
                Select Entity Type: <span className="text-red-600">*</span>
                <span className="ml-4">
                  <label className="inline-flex items-center gap-2 mr-4">
                    <input
                      type="radio"
                      name="entityType"
                      value="Driver"
                      checked={formData.entityType === 'Driver'}
                      onChange={() => handleEntityTypeChange('Driver')}
                      className="accent-blue-500"
                    />
                    <span className="text-xs">Driver</span>
                  </label>
                  <label className="inline-flex items-center gap-2">
                    <input
                      type="radio"
                      name="entityType"
                      value="Vehicle"
                      checked={formData.entityType === 'Vehicle'}
                      onChange={() => handleEntityTypeChange('Vehicle')}
                      className="accent-blue-500"
                    />
                    <span className="text-xs">Vehicle</span>
                  </label>
                </span>
              </label>
            </div>

            <div>
              <div className="flex gap-1">
                <label
                  htmlFor="documentname"
                  className="text-[10px]"
                >
                  Document Name <span className="text-red-600">*</span>
                </label>
              </div>
              <input
                type="text"
                id="documentname"
                name="documentname"
                value={formData?.documentname}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                required
                placeholder="Enter document name"
              />
            </div>

            <div>
              <label className="text-[10px] mb-2 block">
                Document Verification Required
              </label>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="isActive"
                    value="true"
                    checked={formData.isActive === true}
                    onChange={() =>
                      handleChange({
                        target: { name: "isActive", value: true },
                      })
                    }
                    className="accent-green-500"
                  />
                  <span className="text-xs">Required for Active Driver/Vehicle</span>
                </label>
              </div>
            </div>
          </div>

          <div className="flex gap-[10px] justify-end">
            <button
              type="button"
              onClick={onClose}
              className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
              disabled={loading}
            >
              {loading ? "Updating..." : "Update"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateDocumentModel;
