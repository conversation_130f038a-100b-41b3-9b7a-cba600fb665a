"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { IoIosArrowBack } from "react-icons/io";
import { getAuthData, isAuthenticated } from "@/utils/verifytoken";
import { usePathname } from "next/navigation";
import Image from "next/image";

const Sidebar = () => {
  const [role, setrole] = useState("");
  const [flag, setflag] = useState("");

  const pathname = usePathname();


  useEffect(() => {
    if (isAuthenticated()) {
      const authData = getAuthData();
      setrole(authData?.role);
      setflag(authData?.flag);
    } else {
      console.log("User is not authenticated");
      return;
    }
  }, []);

  const [isOpenManagement, setIsOpenManagement] = useState(false);
  const [isOpenReports, setIsOpenReports] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem("isOpenManagement");
      if (savedValue) {
        setIsOpenManagement(JSON.parse(savedValue));
      }
      const savedReportsValue = localStorage.getItem("isOpenReports");
      if (savedReportsValue) {
        setIsOpenReports(JSON.parse(savedReportsValue));
      }
    }
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("isOpenManagement", JSON.stringify(isOpenManagement));
    }
  }, [isOpenManagement]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("isOpenReports", JSON.stringify(isOpenReports));
    }
  }, [isOpenReports]);


  const toggleValue = () => {
    setIsOpenManagement((prev) => !prev);
  };

  const toggleReports = () => {
    setIsOpenReports((prev) => !prev);
  };

  const [activeLink, setActiveLink] = useState("/Dashboard/Home");
  useEffect(() => {
    const path = window?.location?.pathname;
    setActiveLink(path);
  }, []);

  const handleLinkClick = (path) => {
    setActiveLink(path);
  };

  const handleCardClick = () => {
    window.location.reload();
    localStorage.setItem("flag", "false");
    localStorage.setItem("Iscompanyselected", "No");
    localStorage.removeItem("companyID");
    localStorage.removeItem("companyName");
  };

  const [zoomLevel, setZoomLevel] = useState(100);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setZoomLevel(window.devicePixelRatio * 100);

      const handleZoom = () => {
        setZoomLevel(window.devicePixelRatio * 100);
      };

      window.addEventListener('resize', handleZoom);

      return () => {
        window.removeEventListener('resize', handleZoom);
      };
    }
  }, []);
  return (

    <div className={`relative h-[100%] ${zoomLevel > 100 ? "w-[20%]" : "w-[15%]"
      } `}>
      <aside
        className={`bg-white-800 text-black h-[100vh]  w-full flex flex-col relative `}
      >
        <nav
          className="flex-1  "
          style={{
            backgroundImage: "url('/bgSideBar.png')",
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <ul className="space-y-1 bg-transparent mt-5">
            <Link passHref href="/Dashboard/Home">
              <li
                onClick={() => handleLinkClick("/Dashboard/Home")}
                className={`${activeLink === "/Dashboard/Home"
                  ? " border-b-2 border-white"
                  : "text-blue "
                  } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
              >
                <div className="flex items-center gap-3 hover:text-black  bg-transparent">
                  <Image width={20} height={20} src="/dashboard.png" alt="dashboard" />
                  <span className="hidden sm:block text-sm text-white  bg-transparent ">
                    Dashboard
                  </span>
                </div>
              </li>
            </Link>
            {role === "superadmin" && flag === "false" ? (
              <>

                <Link passHref href="/Dashboard/Superadmin">
                  <li
                    onClick={() => handleLinkClick("/Dashboard/Superadmin")}
                    className={`${activeLink === "/Dashboard/Superadmin"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image
                        width={20} height={20}
                        src="/register.png"
                        alt="regitstercompany"
                      />
                      <span className="hidden sm:block text-sm text-white bg-transparent">
                        All Companies
                      </span>
                    </div>
                  </li>
                </Link>


                <Link passHref href={"/Dashboard/Users/<USER>"}>
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Users/<USER>")
                    }
                    className={`${activeLink === "/Dashboard/Users/<USER>"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image
                       width={20} height={20}
                        src="/superadmin.png"
                        alt="superadmin"
                      />

                      <span className="hidden sm:block text-sm text-white bg-transparent">
                        Super Admins
                      </span>
                    </div>
                  </li>
                </Link>

                <div className="bg-transparent">
                  <li

                    className={`flex items-center p-3 cursor-pointer hover:border-b-2  bg-transparent ${isOpenManagement ? "border-b-2" : "text-blue"
                      } ${activeLink ===
                        "/Dashboard/Models/Manufacturer/GetManufacturers" ||
                        activeLink ===
                        "/Dashboard/Models/CarModels/GetCarsModels" ||
                        activeLink ===
                        "/Dashboard/Models/Type_BodyStyle/GetTypes" ||
                        activeLink ===
                        "/Dashboard/Models/FuelType/GetFuelTypes" ||
                        activeLink ===
                        "/Dashboard/Models/Transmission/GetTransmissions"
                        ? " border-b-2 "
                        : "text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 relative bg-transparent w-full">
                      <div
                        className="flex bg-transparent gap-3 w-full"
                        onClick={toggleValue}
                      >
                        <Image
                          width={20} height={20}
                          src="/setting.png"
                          alt="superadmin"
                        />
                        <p className="hidden sm:block text-sm bg-transparent text-white w-full">
                          Vehicle Settings
                        </p>
                      </div>

                      {isOpenManagement && (
                        <div className="absolute md:top-12  rounded-4 py-2 top-9 md:py-0 bg-[#38384A]  text-white md:bg-transparent  w-40 md:w-full z-50 ">
                          <ul className="space-y-2 pl-6 bg-transparent text-white w-full list-disc lg:marker:text-[#D9D9D9] marker:text-lg lg:marker:text-xl">
                            {[
                              {
                                path: "/Dashboard/Models/Manufacturer/GetManufacturers",
                                label: "Manufacturers",
                              },
                              {
                                path: "/Dashboard/Models/CarModels/GetCarsModels",
                                label: "Models",
                              },
                              {
                                path: "/Dashboard/Models/Type_BodyStyle/GetTypes",
                                label: "Body Type",
                              },
                              {
                                path: "/Dashboard/Models/FuelType/GetFuelTypes",
                                label: "Fuel Type",
                              },
                              {
                                path: "/Dashboard/Models/Transmission/GetTransmissions",
                                label: "Transmission Type",
                              },
                            ].map((item) => (
                              <li
                                key={item?.path}
                                className=" font-medium  text-xs lg:text-sm w-full bg-transparent text-white"
                              >
                                <Link
                                  href={item?.path}
                                  className={`w-full block bg-transparent ${pathname === item?.path
                                    ? "opacity-100"
                                    : "opacity-65"
                                    }`}
                                >
                                  {item.label}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </li>
                </div>
              </>
            ) : role === "superadmin" && flag === "true" ? (
              <>
                <Link passHref href="/Dashboard/Home">
                  <li
                    className={`${activeLink === "/Dashboard/Home"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div
                      className="flex items-center gap-3 bg-transparent"
                      onClick={handleCardClick}
                    >
                      <IoIosArrowBack className="text-white text-sm bg-transparent" />
                      <span className="hidden sm:block bg-transparent text-white">
                        Go Back Superadmin
                      </span>
                    </div>
                  </li>
                </Link>

                <Link passHref href={"/Dashboard/Users/<USER>"}>
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Users/<USER>")
                    }
                    className={`${activeLink === "/Dashboard/Users/<USER>"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/user.png" alt="user" />
                      <span className="hidden sm:block text-sm bg-transparent text-white">
                        Users
                      </span>
                    </div>
                  </li>
                </Link>

                <Link passHref href="/Dashboard/Driver/GetAllDrivers">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Driver/GetAllDrivers")
                    }
                    className={`${activeLink === "/Dashboard/Driver/GetAllDrivers"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/driver.png" alt="user" />
                      <span className="hidden sm:block text-sm bg-transparent text-white">
                        Driver
                      </span>
                    </div>
                  </li>
                </Link>

                <Link passHref href="/Dashboard/Vehicle/GetAllVehicle">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Vehicle/GetAllVehicle")
                    }
                    className={`${activeLink === "/Dashboard/Vehicle/GetAllVehicle"
                      ? "border-b-2"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/driver.png" alt="user"/>

                      <span className="hidden sm:block text-sm bg-transparent text-white">
                        Vehicle
                      </span>
                    </div>
                  </li>
                </Link>

                <Link passHref href="/Dashboard/Vehicle/FindVehicleInfo">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Vehicle/FindVehicleInfo")
                    }
                    className={`${activeLink === "/Dashboard/Vehicle/FindVehicleInfoe"
                      ? " border-b-2 border-white"
                      : "text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 mx-2 bg-transparent`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/vehicle.png" alt="user"/>

                      <span className="hidden sm:block text-sm text-white bg-transparent">
                        Vehicle Info
                      </span>
                    </div>
                  </li>
                </Link>

                <div className="bg-transparent">
                  <li
                    className={`flex items-center p-3 cursor-pointer hover:border-b-2  bg-transparent ${
                      isOpenReports ? " border-b-2 " : "text-blue"
                    } ${
                      activeLink === "/Dashboard/Models/DocumentManagment/DocumentReport"
                        ? " border-b-2 "
                        : "text-blue"
                    } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent w-full">
                      <div
                        className="flex bg-transparent gap-3 w-full"
                        onClick={toggleReports}
                      >
                        <Image
                          width={20} height={20}
                          src="/vehicle.png"
                          alt="reports"
                        />
                        <p className="hidden sm:block text-sm bg-transparent text-white w-full">
                          Reports
                        </p>
                      </div>
                    </div>
                  </li>
                  {isOpenReports && (
                    <div className="bg-transparent w-full">
                      <ul className="space-y-2 pl-6 bg-transparent text-white w-full list-disc lg:marker:text-[#D9D9D9] marker:text-lg lg:marker:text-xl ml-8">
                        <li className=" font-medium  text-xs lg:text-sm w-full bg-transparent text-white">
                          <Link
                            href="/Dashboard/Models/DocumentManagment/DocumentReport"
                            className={`w-full block bg-transparent ${
                              pathname === "/Dashboard/Models/DocumentManagment/DocumentReport"
                                ? "opacity-100"
                                : "opacity-65"
                            }`}
                          >
                            Document Reports
                          </Link>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>

                <div className="bg-transparent w-full">
                  <li
                    className={`flex items-center p-3 cursor-pointer hover:border-b-2  bg-transparent ${isOpenManagement
                      ? " border-b-2 "
                      : "text-blue"
                      } ${activeLink ===
                        "/Dashboard/Models/Manufacturer/GetManufacturers" ||
                        activeLink ===
                        "/Dashboard/Models/CarModels/GetCarsModels" ||
                        activeLink === "/Dashboard/Models/Enquiry/GetEnquiries" ||
                        activeLink === "/Dashboard/Models/Firm/GetFirms" ||
                        activeLink ===
                        "/Dashboard/Models/Signature/GetSignatures" ||
                        activeLink ===
                        "/Dashboard/Models/LocalAuthority/GetLocalAuthority" ||
                        activeLink ===
                        "/Dashboard/Models/Supplier/GetSuppliers" ||
                        activeLink ===
                        "/Dashboard/Models/Insurance/GetInsurances" ||
                        activeLink ===
                        "/Dashboard/Models/Type_BodyStyle/GetTypes" ||
                        activeLink ===
                        "/Dashboard/Models/FuelType/GetFuelTypes" ||
                        activeLink ===
                        "/Dashboard/Models/Transmission/GetTransmissions"
                        ? "border-l-4 border-b-2 border-red-400"
                        : "text-blue"
                      }`}
                  >
                    <div className="flex  items-center gap-3 relative bg-transparent w-full"
                      onClick={toggleValue}
                    >

                      <Image
                        width={20} height={20}
                        src="/setting.png"
                        alt="superadmin"
                        className="w-5"
                      />

                      <p className="hidden sm:block text-sm bg-transparent text-white w-full">
                        Settings
                      </p>
                      <div />
                      {isOpenManagement && (
                        <div className="absolute h-[60vh] md:top-12 custom-scrollbar  rounded-4 py-2 top-9 md:py-0 bg-[#38384A] text-white md:bg-transparent w-40  md:w-full z-50">
                          <ul className="space-y-2 pl-6 bg-transparent text-white w-full list-disc lg:marker:text-[#D9D9D9] marker:text-lg lg:marker:text-xl">
                            {[
                              {
                                path: "/Dashboard/Models/Manufacturer/GetManufacturers",
                                label: "Manufacturers",
                              },
                              {
                                path: "/Dashboard/Models/CarModels/GetCarsModels",
                                label: "Models",
                              },
                              {
                                path: "/Dashboard/Models/Type_BodyStyle/GetTypes",
                                label: "Body Type",
                              },
                              {
                                path: "/Dashboard/Models/FuelType/GetFuelTypes",
                                label: "Fuel Type",
                              },
                              {
                                path: "/Dashboard/Models/Transmission/GetTransmissions",
                                label: "Transmission Type",
                              },
                              {
                                path: "/Dashboard/Models/VehicleType/GetVehicleTypes",
                                label: "Vehicle Types",
                              },
                              {
                                path: "/Dashboard/Models/Enquiry/GetEnquiries",
                                label: "Enquiries",
                              },
                              {
                                path: "/Dashboard/Models/Firm/GetFirms",
                                label: "Firms",
                              },
                              {
                                path: "/Dashboard/Models/Signature/GetSignatures",
                                label: "Signatures",
                              },
                              {
                                path: "/Dashboard/Models/LocalAuthority/GetLocalAuthority",
                                label: "Local Authority",
                              },
                              {
                                path: "/Dashboard/Models/Supplier/GetSuppliers",
                                label: "Suppliers",
                              },
                              {
                                path: "/Dashboard/Models/Insurance/GetInsurances",
                                label: "Insurances",
                              },

                            ].map((item) => (
                              <li
                                key={item.path}
                                className="font-medium text-xs lg:text-sm w-full bg-transparent text-white"
                              >
                                <Link
                                  href={item.path}
                                  className={`w-full block bg-transparent ${pathname === item.path
                                    ? "opacity-100"
                                    : "opacity-65"
                                    }`}
                                >
                                  {item.label}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </li>
                </div>
              </>
            ) : (
              <>
                <Link passHref href={"/Dashboard/Users/<USER>"}>
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Users/<USER>")
                    }
                    className={`${activeLink === "/Dashboard/Users/<USER>"
                      ? " border-b-2 border-white"
                      : " text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2  mx-2 bg-transparent`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/user.png" alt="user" />
                      <span className="hidden sm:block text-sm bg-transparent text-white">
                        Users
                      </span>
                    </div>
                  </li>
                </Link>
                <Link passHref href="/Dashboard/Driver/GetAllDrivers">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Driver/GetAllDrivers")
                    }
                    className={`${activeLink === "/Dashboard/Driver/GetAllDrivers"
                      ? "border-b-2 border-white"
                      : "text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 mx-2 bg-transparent`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/driver.png" alt="user" />
                      <span className="hidden sm:block text-sm bg-transparent text-white">
                        Driver
                      </span>
                    </div>
                  </li>
                </Link>
                <Link passHref href="/Dashboard/Vehicle/GetAllVehicle">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Vehicle/GetAllVehicle")
                    }
                    className={`${activeLink === "/Dashboard/Vehicle/GetAllVehicle"
                      ? " border-b-2 border-white"
                      : "text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 mx-2 bg-transparent`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/vehicle.png" alt="user"/>

                      <span className="hidden sm:block text-sm text-white bg-transparent">
                        Vehicle
                      </span>
                    </div>
                  </li>
                </Link>
                <Link passHref href="/Dashboard/Vehicle/FindVehicleInfo">
                  <li
                    onClick={() =>
                      handleLinkClick("/Dashboard/Vehicle/FindVehicleInfo")
                    }
                    className={`${activeLink === "/Dashboard/Vehicle/FindVehicleInfoe"
                      ? " border-b-2 border-white"
                      : "text-blue"
                      } flex items-center p-3 cursor-pointer hover:border-b-2 mx-2 bg-transparent`}
                  >
                    <div className="flex items-center gap-3 bg-transparent">
                      <Image width={20} height={20} src="/vehicle.png" alt="user"  />

                      <span className="hidden sm:block text-sm text-white bg-transparent">
                        Vehicle Info
                      </span>
                    </div>
                  </li>
                </Link>
                <div className="bg-transparent">
                  <li
                    className={`flex items-center p-3 cursor-pointer hover:border-b-2  bg-transparent ${
                      isOpenReports ? " border-b-2 border-white" : "text-blue"
                    } ${
                      activeLink === "/Dashboard/Models/DocumentManagment/DocumentReport"
                        ? " border-b-2 border-white"
                        : "text-blue"
                    } flex items-center p-3 cursor-pointer hover:border-b-2 bg-transparent mx-2`}
                  >
                    <div className="flex items-center gap-3 bg-transparent w-full">
                      <div
                        className="flex bg-transparent gap-3 w-full"
                        onClick={toggleReports}
                      >
                        <Image
                          width={20} height={20}
                          src="/vehicle.png"
                          alt="reports"
                        />
                        <p className="hidden sm:block text-sm bg-transparent text-white w-full">
                          Reports
                        </p>
                      </div>
                    </div>
                  </li>
                  {isOpenReports && (
                    <div className="bg-transparent w-full">
                      <ul className="space-y-2 pl-6 bg-transparent text-white w-full list-disc lg:marker:text-[#D9D9D9] marker:text-lg lg:marker:text-xl ml-8">
                        <li className=" font-medium  text-xs lg:text-sm w-full bg-transparent text-white">
                          <Link
                            href="/Dashboard/Models/DocumentManagment/DocumentReport"
                            className={`w-full block bg-transparent ${
                              pathname === "/Dashboard/Models/DocumentManagment/DocumentReport"
                                ? "opacity-100"
                                : "opacity-65"
                            }`}
                          >
                            Documents Report
                          </Link>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
                <div className="bg-transparent">
                  <li
                    className={`flex items-center p-3 cursor-pointer hover:border-b-2 mx-2 bg-transparent ${isOpenManagement ? "border-b-2 border-white" : "text-blue"
                      } ${activeLink ===
                        "/Dashboard/Models/Manufacturer/GetManufacturers" ||
                        activeLink ===
                        "/Dashboard/Models/CarModels/GetCarsModels" ||
                        activeLink === "/Dashboard/Models/Enquiry/GetEnquiries" ||
                        activeLink === "/Dashboard/Models/Firm/GetFirms" ||
                        activeLink ===
                        "/Dashboard/Models/Signature/GetSignatures" ||
                        activeLink ===
                        "/Dashboard/Models/LocalAuthority/GetLocalAuthority" ||
                        activeLink ===
                        "/Dashboard/Models/Supplier/GetSuppliers" ||
                        activeLink ===
                        "/Dashboard/Models/Insurance/GetInsurances" ||
                        activeLink ===
                        "/Dashboard/Models/Type_BodyStyle/GetTypes" ||
                        activeLink ===
                        "/Dashboard/Models/FuelType/GetFuelTypes" ||
                        activeLink ===
                        "/Dashboard/Models/Transmission/GetTransmissions" || activeLink === "/Dashboard/Models/DriverBalance/GetDriverBalance" ||
                        activeLink === "/Dashboard/Models/DriverCars/GetDriverCars"
                        ? " border-b-2 border-white"
                        : "text-blue"
                      }`}
                  >
                    <div className="flex items-center gap-3 relative bg-transparent w-full"
                      onClick={toggleValue}
                    >
                      <Image
                        width={20} height={20}
                        src="/setting.png"
                        alt="superadmin"
                      />

                      <p className="hidden sm:block text-sm bg-transparent text-white">Settings</p>

                      {isOpenManagement && (
                        <div className="absolute md:top-12 rounded-4 top-9 md:py-0 bg-red-200 text-white md:bg-transparent w-40 md:w-full z-50 h-[55vh]  custom-scrollbar">

                          <ul className="space-y-1 pl-6 bg-transparent text-white w-full list-disc lg:marker:text-[#D9D9D9] marker:text-lg max-h-[50vh]">
                            {[
                              {
                                path: "/Dashboard/Models/DocumentManagment/AddDocument",
                                label: "Document Managment",
                              },
                              {
                                path: "/Dashboard/Models/DriverBalance/GetAllDrivers",
                                label: "Balance",
                              },
                              {
                                path: "/Dashboard/Models/DriverCars/GetDriverCars",
                                label: "Cars",
                              },
                              {
                                path: "/Dashboard/Models/Manufacturer/GetManufacturers",
                                label: "Manufacturers",
                              },
                              {
                                path: "/Dashboard/Models/CarModels/GetCarsModels",
                                label: "Models",
                              },
                              {
                                path: "/Dashboard/Models/Type_BodyStyle/GetTypes",
                                label: "Body Type",
                              },
                              {
                                path: "/Dashboard/Models/FuelType/GetFuelTypes",
                                label: "Fuel Type",
                              },
                              {
                                path: "/Dashboard/Models/Transmission/GetTransmissions",
                                label: "Transmission Type",
                              },
                              {
                                path: "/Dashboard/Models/VehicleType/GetVehicleTypes",
                                label: "Vehicle Types",
                              },
                              {
                                path: "/Dashboard/Models/Enquiry/GetEnquiries",
                                label: "Enquiries",
                              },
                              {
                                path: "/Dashboard/Models/Firm/GetFirms",
                                label: "Firms",
                              },
                              {
                                path: "/Dashboard/Models/Signature/GetSignatures",
                                label: "Signatures",
                              },
                              {
                                path: "/Dashboard/Models/LocalAuthority/GetLocalAuthority",
                                label: "Local Authority",
                              },
                              {
                                path: "/Dashboard/Models/Supplier/GetSuppliers",
                                label: "Suppliers",
                              },
                              {
                                path: "/Dashboard/Models/Insurance/GetInsurances",
                                label: "Insurances",
                              },
                            ].map((item) => (
                              <li key={item.path} className="font-medium text-xs lg:text-sm w-full bg-transparent text-white">
                                <Link
                                  href={item.path}
                                  className={`w-full block bg-transparent ${pathname === item.path ? "opacity-100" : "opacity-65"
                                    }`}
                                >
                                  {item.label}
                                </Link>
                              </li>
                            ))}
                          </ul>
                        </div>

                      )}
                    </div>
                  </li>
                </div>
              </>
            )}
          </ul>
        </nav>
      </aside >
    </div >
  );
};

export default Sidebar;