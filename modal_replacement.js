                <tbody>
                  {/* Dynamic Document Row - Selected Document */}
                  <tr className="border-b border-gray-100 bg-blue-50">
                    <td className="py-3 px-0 text-gray-700 text-sm font-medium">
                      {selectedDocument.documentname ? 
                        selectedDocument.documentname.charAt(0).toUpperCase() + selectedDocument.documentname.slice(1) 
                        : 'Document'
                      }
                    </td>
                    <td className="py-3 px-4 text-gray-700 text-sm">
                      {formatDate(selectedDocument.createdAt)}
                    </td>
                    <td className="py-3 px-4 text-gray-700 text-sm">
                      {formatDate(selectedDocument.documentExpire)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-2 ${getStatusDisplay(selectedDocument.verificationStatus).color}`}></div>
                        <span className={`text-sm ${getStatusDisplay(selectedDocument.verificationStatus).textColor}`}>
                          {getStatusDisplay(selectedDocument.verificationStatus).text}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        {(selectedDocument.verificationStatus || 'pending') === 'pending' && (
                          <>
                            <button className="text-orange-600 hover:text-orange-800 text-sm flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                              </svg>
                              Verify
                            </button>
                            <span className="text-gray-300">/</span>
                          </>
                        )}
                        <button 
                          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                          onClick={() => selectedDocument.document && window.open(getDocumentUrl(selectedDocument.document), '_blank')}
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Additional Information Row */}
                  {selectedDocument.Driverid && (
                    <tr className="border-b border-gray-100 bg-gray-50">
                      <td className="py-2 px-0 text-gray-600 text-xs" colSpan="5">
                        <div className="flex flex-wrap gap-4">
                          <span><strong>Document ID:</strong> {selectedDocument._id}</span>
                          {selectedDocument.Driverid.licenseNumber && (
                            <span><strong>License:</strong> {selectedDocument.Driverid.licenseNumber}</span>
                          )}
                          {selectedDocument.Driverid.email && (
                            <span><strong>Email:</strong> {selectedDocument.Driverid.email}</span>
                          )}
                          {selectedDocument.Driverid.tel1 && (
                            <span><strong>Phone:</strong> {selectedDocument.Driverid.tel1}</span>
                          )}
                          {selectedDocument.Driverid.city && selectedDocument.Driverid.county && (
                            <span><strong>Location:</strong> {selectedDocument.Driverid.city}, {selectedDocument.Driverid.county}</span>
                          )}
                          {selectedDocument.Driverid.postcode && (
                            <span><strong>Postcode:</strong> {selectedDocument.Driverid.postcode}</span>
                          )}
                          {selectedDocument.adminCompanyName && (
                            <span><strong>Company:</strong> {selectedDocument.adminCompanyName}</span>
                          )}
                          {selectedDocument.Driverid.niNumber && (
                            <span><strong>NI Number:</strong> {selectedDocument.Driverid.niNumber}</span>
                          )}
                          {selectedDocument.Driverid.licenseExpiryDate && (
                            <span><strong>License Expires:</strong> {formatDate(selectedDocument.Driverid.licenseExpiryDate)}</span>
                          )}
                          {selectedDocument.Driverid.taxiBadgeDate && (
                            <span><strong>Badge Date:</strong> {formatDate(selectedDocument.Driverid.taxiBadgeDate)}</span>
                          )}
                          {selectedDocument.verifiedBy && (
                            <span><strong>Verified By:</strong> {selectedDocument.verifiedBy}</span>
                          )}
                          {selectedDocument.verifiedAt && (
                            <span><strong>Verified On:</strong> {formatDate(selectedDocument.verifiedAt)}</span>
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
