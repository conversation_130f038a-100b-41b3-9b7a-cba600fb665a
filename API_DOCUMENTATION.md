# Document Verification API Documentation

## Overview
This document describes the API endpoints and data structures for the document verification functionality in the Vehicle Management System.

## Implementation Status
✅ **COMPLETED**: Document verification functionality has been successfully implemented with the following features:

### Frontend Features
- **Modal Design**: Exact replica of the provided design with proper styling and layout
- **Document Table**: Shows multiple document types with status indicators (verified, new, expired, about to expire)
- **Status Indicators**: Color-coded dots (green, yellow, red) matching the design
- **PDF Preview**: Placeholder PDF preview section as shown in the design
- **Verification Options**: Radio buttons for Verified/Rejected selection
- **Responsive Design**: Proper modal sizing and responsive behavior
- **Error Handling**: Comprehensive error handling for API calls and missing data

### Backend Features
- **Database Schema**: Added verification fields to DriverDocuments model
- **API Authentication**: JWT token validation for secure operations
- **Status Tracking**: Tracks verification status, verifier, and timestamp
- **Data Validation**: Proper validation of verification status values

### Data Handling
- **Default Values**: Handles missing verification status fields gracefully
- **Null Safety**: Proper handling of null/undefined Driverid objects
- **API Integration**: Real API calls with proper error handling and success notifications

## API Endpoints

### Update Document Verification Status
**Endpoint:** `PUT /api/DriverDocuments/{documentId}`

**Description:** Updates the verification status of a specific driver document.

**Authentication:** Required (Bearer Token)

**Request Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "verificationStatus": "verified" | "rejected" | "pending",
  "verifiedBy": "string (username of verifier)"
}
```

**Response (Success - 200):**
```json
{
  "message": "Document updated successfully",
  "data": {
    "_id": "document_id",
    "document": "document_url",
    "documentname": "document_type",
    "documentExpire": "expiry_date",
    "Driverid": "driver_id",
    "verificationStatus": "verified",
    "verifiedBy": "username",
    "verifiedAt": "2025-01-14T10:30:00.000Z",
    "isActive": true,
    "adminCreatedBy": "admin_name",
    "adminCompanyName": "company_name",
    "adminCompanyId": "company_id",
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-14T10:30:00.000Z"
  },
  "status": 200
}
```

**Response (Error - 404):**
```json
{
  "error": "Document not found",
  "status": 404
}
```

**Response (Error - 401):**
```json
{
  "error": "Invalid token",
  "status": 401
}
```

### Get All Driver Documents
**Endpoint:** `GET /api/DriverDocuments`

**Description:** Retrieves all driver documents with populated driver information.

**Response (Success - 200):**
```json
{
  "result": [
    {
      "_id": "document_id",
      "document": "document_url",
      "documentname": "document_type",
      "documentExpire": "expiry_date",
      "Driverid": {
        "_id": "driver_id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "licenseNumber": "DL123456",
        "insurance": "INS789",
        "licenseExpiryDate": "2025-12-31",
        "taxiBadgeDate": "2024-01-01"
      },
      "verificationStatus": "pending",
      "verifiedBy": null,
      "verifiedAt": null,
      "isActive": true,
      "adminCreatedBy": "admin_name",
      "adminCompanyName": "company_name",
      "adminCompanyId": "company_id",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

## Data Models

### DriverDocument Schema
```javascript
{
  document: String (required) - URL or path to the document file
  documentExpire: String (required) - Document expiration date
  documentname: String - Type/name of the document
  Driverid: ObjectId (ref: 'Driver') - Reference to the driver
  isActive: Boolean (default: false) - Whether the document is active
  verificationStatus: String (enum: ['pending', 'verified', 'rejected'], default: 'pending')
  verifiedBy: String (default: null) - Username of the person who verified
  verifiedAt: Date (default: null) - Timestamp when verification occurred
  adminCreatedBy: String - Admin who created the document
  adminCompanyName: String - Company name
  adminCompanyId: String - Company ID
}
```

### Verification Status Values
- `pending`: Document is awaiting verification (default)
- `verified`: Document has been approved/verified
- `rejected`: Document has been rejected and needs to be resubmitted

## Frontend Integration

### Modal Trigger
The verification modal is triggered by clicking the green checkmark (verify) button in each document row.

### Modal Features
- Displays selected document details
- Shows current verification status
- Allows selection of new verification status (Verified/Rejected)
- Includes PDF preview placeholder
- Provides Cancel and Submit actions

### Success/Error Handling
- Success: Shows toast notification and refreshes document list
- Error: Shows appropriate error message via toast notification
- Authentication: Redirects to login if token is invalid

## Security Notes
- All verification operations require valid JWT authentication
- Only authenticated users can update document verification status
- Verification actions are logged with timestamp and verifier information
