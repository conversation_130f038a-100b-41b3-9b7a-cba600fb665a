// models/DocumentManagement.Model.js
import mongoose from "mongoose";

// Define the schema
const BadgeSchema = new mongoose.Schema({
  documentname: {
    type: String,
    required: true,
    trim: true, // Removes whitespace from the beginning and end
  },
  entityType: {
    type: [String], // Array of strings to store ["Driver", "Vehicle"] or both
    required: true,
    enum: ["Driver", "Vehicle"], // Only allow these values
  },
  description: {
    type: String,
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: false, // Defaults to false if not specified
  },
  adminCreatedBy: { type: String },
  adminCompanyName: { type: String },
  adminCompanyId: { type: String },
}, {
  timestamps: true // Adds createdAt and updatedAt fields
});

// Create the model
export default mongoose.models.Badge || mongoose.model("Badge", BadgeSchema);
