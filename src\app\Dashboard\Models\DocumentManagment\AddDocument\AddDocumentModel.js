"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import { API_URL_Document } from "../../../Components/ApiUrl/ApiUrls";
import { getCompanyName, getUserName, getcompanyId } from "@/utils/storageUtils";
import Image from "next/image";
import { useRouter } from "next/navigation";

const AddDocumentModel = ({ isOpen, onClose, fetchData }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    documentname: "",
    entityType: "",
    isActive: false,
    adminCreatedBy: "",
    adminCompanyName: "",
    companyId: "",
  });

  const initialFormData = {
    documentname: "",
    entityType: "",
    isActive: false,
    adminCreatedBy: "",
    adminCompanyName: "",
    companyId: "",
  };

  useEffect(() => {
    const companyNameFromStorage = (() => {
      const name1 = getCompanyName();
      if (name1) return name1;

      const name2 = getUserName();
      if (name2) return name2;
    })();

    const companyIdFromStorage = getcompanyId();

    if (companyNameFromStorage) {
      setFormData(prev => ({
        ...prev,
        adminCompanyName: companyNameFromStorage,
        adminCreatedBy: companyNameFromStorage,
        companyId: companyIdFromStorage || "",
      }));
    }
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleEntityTypeChange = (entityType) => {

    setFormData(prev => ({
      ...prev,
      entityType: entityType
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();


    if (!formData.entityType) {
      toast.error("Please select an entity type (Driver or Vehicle)");
      return;
    }

    setLoading(true);

    try {
      const token = localStorage.getItem("token");
      console.log("=== ADD DOCUMENT DEBUG LOGS ===");
      console.log("Retrieved token:", token ? `Present (length: ${token.length}, starts with: ${token.substring(0, 10)}...)` : "MISSING (null or undefined)");
      console.log("API URL:", API_URL_Document);
      console.log("Form data being sent:", JSON.stringify(formData, null, 2));
      console.log("Authorization header will be:", token ? `Bearer ${token.substring(0, 10)}...` : "Invalid (no token)");

      if (!token) {
        throw new Error("No token found in localStorage");
      }


      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const now = Date.now() / 1000;
        console.log("Token JWT claims:", {
          iss: payload.iss || "undefined",
          exp: payload.exp ? new Date(payload.exp * 1000).toISOString() : "no exp",
          iat: payload.iat ? new Date(payload.iat * 1000).toISOString() : "no iat",
          expired: payload.exp ? (payload.exp < now) : "unknown",
          companyRelated: payload.companyId || payload.company || "none in token"
        });
      } catch (decodeErr) {
        console.log("JWT decode error:", decodeErr.message);
      }

      const response = await axios.post(API_URL_Document, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      console.log("API Response:", response.data);

      if (response.data.success) {
        toast.success(response?.data?.message || "Document added successfully");
        fetchData();
        onClose();
        setFormData(initialFormData);
        // Navigate to DocumentReport page
        router.push("/Dashboard/Models/DocumentManagment/DocumentReport");
      } else {
        console.log("Non-success response details:", {
          error: response.data.error,
          message: response.data.message,
          status: response.status
        });
        toast.warn(response?.data?.error || response?.data?.message || "Failed to add document");
      }
    } catch (err) {
      console.error("=== FULL ERROR DETAILS ===");
      console.error("Error object:", err);
      console.error("Error message:", err.message);
      console.error("Response status:", err.response?.status);
      console.error("Response data:", err.response?.data);
      console.error("Request config:", err.config);
      toast.error(err.response?.data?.message || err.response?.data?.error || err.message || "Failed to add document");
    } finally {
      setLoading(false);
      console.log("=== END DEBUG LOGS ===");
    }
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white p-[50px] w-[528px] rounded-xl shadow-lg h-[380px]">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            Add Document Type
          </h2>

          <Image
            width={15}
            height={15}
            alt="cross"
            src="/crossIcon.svg"
            className="cursor-pointer"
            onClick={() => {
              onClose();
              setFormData(initialFormData);
            }}
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="text-[10px] mb-2 block">
                Select Entity Type: <span className="text-red-600">*</span>
                <span className="ml-4">
                  <label className="inline-flex items-center gap-2 mr-4">
                    <input
                      type="radio"
                      name="entityType"
                      checked={formData.entityType === 'Driver'}
                      onChange={() => handleEntityTypeChange('Driver')}
                      className="accent-blue-500"
                    />
                    <span className="text-xs">Driver</span>
                  </label>
                  <label className="inline-flex items-center gap-2">
                    <input
                      type="radio"
                      name="entityType"
                      checked={formData.entityType === 'Vehicle'}
                      onChange={() => handleEntityTypeChange('Vehicle')}
                      className="accent-blue-500"
                    />
                    <span className="text-xs">Vehicle</span>
                  </label>
                </span>
              </label>
            </div>

            <div>
              <div className="flex gap-1">
                <label
                  htmlFor="documentname"
                  className="text-[10px]"
                >
                    Name <span className="text-red-600">*</span>
                </label>
              </div>
              <input
                type="text"
                id="documentname"
                name="documentname"
                value={formData?.documentname}
                onChange={handleChange}
                className="mt-1 block w-full p-2 border border-[#42506666] rounded shadow focus:ring-blue-500 focus:border-blue-500"
                required

              />
            </div>

            <div>
              <div className="flex gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive === true}
                    onChange={(e) =>
                      handleChange({
                        target: { name: "isActive", value: e.target.checked },
                      })
                    }
                  />
                  <span className="text-xs">Document Verification required to Active driver/vehicle</span>
                </label>
              </div>
            </div>
          </div>

          <div className="flex gap-[10px] justify-end">
            <button
              type="button"
              onClick={() => {
                onClose();
                setFormData(initialFormData);
              }}
              className="py-1 px-5 w-full sm:w-auto border-[1px] rounded-4 border-[#313342] bg-white text-[#313342] hover:bg-gray-600 hover:text-white focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#313342] text-white rounded-4 hover:bg-gray-600 focus:ring-4 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-500 py-1 px-8"
              disabled={loading}
            >

              {loading ? "Adding..." : "Add"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDocumentModel;