import { connect } from "@config/db.js";
import VehicleDocument from "@models/VehicleDocuments/VehicleDocuments.Model.js";
import { NextResponse } from "next/server";

/* ========================= UPDATE DOCUMENT ========================= */
export const PUT = async (request, context) => {
  try {
    await connect();
    const id = context.params.VehicleDocumentsID;
    const data = await request.json();

    const {
      document,
      documentname,
      Vehicleid,
      isActive,
      documentExpire,
      adminCreatedBy,
    } = data;

    const doc = await VehicleDocument.findById(id);
    if (!doc) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    const update = {};
    if (document) {
      update.document = document ? document.trim() : VehicleDocument.document;
    }
    if (documentname) {
      update.documentname = documentname ? documentname.trim() : VehicleDocument.documentname;
    }
    if(Vehicleid){
      update.Vehicleid = Vehicleid ? Vehicleid : VehicleDocument.Vehicleid;
    }
    
    if (isActive !== undefined) {
      update.isActive = isActive ? isActive : VehicleDocument.isActive;
    }

    if(adminCreatedBy){
      update.adminCreatedBy = adminCreatedBy ? adminCreatedBy : VehicleDocument.adminCreatedBy;
    }
    if(documentExpire){
      update.documentExpire = documentExpire ? documentExpire : VehicleDocument.documentExpire;
    }

    const updated = await VehicleDocument.findByIdAndUpdate(id, update, { new: true });

    return NextResponse.json({
      message: "Document updated successfully",
      data: updated,
      status: 200,
    });

  } catch (error) {
    console.error("PUT Error:", error);
    return NextResponse.json({
      error: "Failed to update Document",
      status: 500,
    });
  }
};

/* ========================= GET DOCUMENT BY ID ========================= */
export const GET = async (request, context) => {
  try {
    await connect();
    const id = context.params.VehicleDocumentsID;
    const doc = await VehicleDocument.findById(id);

    if (!doc) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    return NextResponse.json({ data: doc, status: 200 });
  } catch (error) {
    console.error("GET Error:", error);
    return NextResponse.json({ error: "Failed to fetch Document", status: 500 });
  }
};

/* ========================= DELETE DOCUMENT ========================= */
export const DELETE = async (request, context) => {
  try {
    await connect();
    const id = context.params.VehicleDocumentsID;

    const deleted = await VehicleDocument.findByIdAndDelete(id);
    if (!deleted) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    return NextResponse.json({
      message: "Document deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("DELETE Error:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the Document",
      status: 500,
    });
  }
};
